#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瀏覽器工具函數
==============

提供瀏覽器相關的工具函數。
"""

import webbrowser
import subprocess
import sys
import os
from typing import Callable


def is_wsl() -> bool:
    """
    检测是否在WSL环境中运行

    Returns:
        bool: True if running in WSL, False otherwise
    """
    # 방법 1: 환경변수 확인 (가장 확실한 방법)
    wsl_env_vars = ['WSL_DISTRO_NAME', 'WSLENV', 'WSL_INTEROP']
    for env_var in wsl_env_vars:
        if os.getenv(env_var):
            print(f"🔍 WSL 감지됨 (환경변수 {env_var}: {os.getenv(env_var)})")
            return True

    # 방법 2: /proc/version 파일 확인
    try:
        if os.path.exists('/proc/version'):
            with open('/proc/version', 'r') as f:
                version_info = f.read().lower()
                if 'microsoft' in version_info or 'wsl' in version_info:
                    print(f"🔍 WSL 감지됨 (/proc/version: {version_info.strip()})")
                    return True
    except Exception as e:
        print(f"⚠️ /proc/version 읽기 실패: {e}")

    # 방법 3: Windows 실행파일 접근 가능성 확인
    try:
        result = subprocess.run(['which', 'cmd.exe'],
                               capture_output=True,
                               text=True,
                               timeout=5)
        if result.returncode == 0 and 'mnt/c' in result.stdout:
            print(f"🔍 WSL 감지됨 (cmd.exe 경로: {result.stdout.strip()})")
            return True
    except Exception as e:
        print(f"⚠️ cmd.exe 확인 실패: {e}")

    # 방법 4: 강제 WSL 감지 (Linux + Windows 파일시스템 마운트 확인)
    try:
        if sys.platform.startswith('linux') and os.path.exists('/mnt/c'):
            print(f"🔍 WSL 감지됨 (Linux + /mnt/c 존재)")
            return True
    except Exception as e:
        print(f"⚠️ /mnt/c 확인 실패: {e}")

    print(f"🔍 WSL 환경이 아님")
    return False


def open_url_in_windows_browser(url: str) -> None:
    """
    在WSL环境中使用Windows浏览器打开URL
    WSL2的特性：Windows可以通过localhost访问WSL服务

    Args:
        url: 要打开的URL
    """
    # WSL2에서 Windows로 접근할 때는 localhost 사용
    windows_url = url
    if '0.0.0.0:' in url:
        # 0.0.0.0을 localhost로 변경
        windows_url = url.replace('0.0.0.0:', 'localhost:')
    elif '127.0.0.1:' in url:
        # 127.0.0.1을 localhost로 변경 (Windows에서 더 안정적)
        windows_url = url.replace('127.0.0.1:', 'localhost:')
    elif '172.29.' in url or '**************' in url:
        # WSL IP나 호스트 IP를 localhost로 변경
        import re
        windows_url = re.sub(r'http://[^:]+:', 'http://localhost:', url)

    print(f"\n" + "="*60)
    print(f"🪟 WSL에서 Windows 브라우저로 열기")
    print(f"="*60)
    print(f"📋 Windows 브라우저에서 아래 URL을 복사하여 열어주세요:")
    print(f"")
    print(f"   {windows_url}")
    print(f"")
    print(f"💡 사용 방법:")
    print(f"   1. 위 URL을 복사하세요 (Ctrl+C)")
    print(f"   2. Windows에서 브라우저를 열어주세요 (Edge, Chrome 등)")
    print(f"   3. 주소창에 붙여넣기 (Ctrl+V)")
    print(f"   4. Enter 키를 눌러 접속하세요")
    print(f"="*60)

    # 자동 브라우저 실행도 시도해보지만 실패해도 정상
    print(f"🔄 자동 브라우저 실행도 시도해보겠습니다...")
    print(f"   (실패해도 정상입니다. 위 URL을 수동으로 사용하세요)")
    
    # 방법 1: cmd.exe를 통해 Edge 명시적 실행 (최우선 순위)
    try:
        print(f"🔧 방법 1: cmd.exe로 Edge 실행 시도...")
        # Windows cmd.exe를 통해 Edge 브라우저로 명시적 실행
        cmd_paths = [
            '/mnt/c/Windows/System32/cmd.exe',
            '/mnt/c/WINDOWS/system32/cmd.exe',
            '/mnt/c/windows/system32/cmd.exe'
        ]

        for cmd_path in cmd_paths:
            if os.path.exists(cmd_path):
                print(f"   cmd.exe 발견: {cmd_path}")
                process = subprocess.Popen([cmd_path, '/c', 'start', 'msedge', windows_url],
                                           stdout=subprocess.DEVNULL,
                                           stderr=subprocess.DEVNULL,
                                           stdin=subprocess.DEVNULL)
                print(f"✅ cmd.exe Edge 실행 시도 완료! (PID: {process.pid})")
                return

        print(f"❌ cmd.exe를 찾을 수 없음")
    except Exception as e:
        print(f"❌ cmd.exe Edge 실행 실패: {e}")
    
    # 방법 2: 직접 Edge 실행파일 호출
    try:
        print(f"🔧 방법 2: Edge 실행파일 직접 호출 시도...")
        edge_paths = [
            "/mnt/c/Program Files (x86)/Microsoft/Edge/Application/msedge.exe",
            "/mnt/c/Program Files/Microsoft/Edge/Application/msedge.exe"
        ]
        
        for edge_path in edge_paths:
            if os.path.exists(edge_path):
                print(f"   Edge 발견: {edge_path}")
                process = subprocess.Popen([edge_path, windows_url],
                                           stdout=subprocess.DEVNULL,
                                           stderr=subprocess.DEVNULL,
                                           stdin=subprocess.DEVNULL)
                print(f"✅ Edge 직접 실행 시도 완료! (PID: {process.pid})")
                return
        
        print(f"❌ Edge 실행파일을 찾을 수 없음")
    except Exception as e:
        print(f"❌ Edge 직접 실행 실패: {e}")
    
    # 방법 3: PowerShell을 통한 Edge 실행
    try:
        print(f"🔧 방법 3: PowerShell로 Edge 실행 시도...")
        powershell_cmd = f'Start-Process "msedge" -ArgumentList "{windows_url}"'
        process = subprocess.Popen(['/mnt/c/Windows/System32/WindowsPowerShell/v1.0/powershell.exe', 
                                   '-Command', powershell_cmd],
                                   stdout=subprocess.DEVNULL,
                                   stderr=subprocess.DEVNULL,
                                   stdin=subprocess.DEVNULL)
        print(f"✅ PowerShell Edge 실행 시도 완료! (PID: {process.pid})")
        return
    except Exception as e:
        print(f"❌ PowerShell Edge 실행 실패: {e}")
    
    # 방법 4: explorer.exe를 통한 URL 실행
    try:
        print(f"🔧 방법 4: explorer.exe로 URL 실행 시도...")
        explorer_paths = [
            '/mnt/c/Windows/explorer.exe',
            '/mnt/c/WINDOWS/explorer.exe',
            '/mnt/c/windows/explorer.exe'
        ]

        for explorer_path in explorer_paths:
            if os.path.exists(explorer_path):
                print(f"   explorer.exe 발견: {explorer_path}")
                process = subprocess.Popen([explorer_path, windows_url],
                                           stdout=subprocess.DEVNULL,
                                           stderr=subprocess.DEVNULL,
                                           stdin=subprocess.DEVNULL)
                print(f"✅ explorer.exe URL 실행 시도 완료! (PID: {process.pid})")
                return

        print(f"❌ explorer.exe를 찾을 수 없음")
    except Exception as e:
        print(f"❌ explorer.exe 실행 실패: {e}")

    # 방법 5: rundll32 사용 (마지막 수단)
    try:
        print(f"🔧 방법 5: rundll32 url.dll,FileProtocolHandler 시도...")
        process = subprocess.Popen(['rundll32.exe', 'url.dll,FileProtocolHandler', windows_url],
                                   stdout=subprocess.DEVNULL,
                                   stderr=subprocess.DEVNULL)
        print(f"✅ rundll32 백그라운드 실행 시도 완료! (PID: {process.pid})")
        return
    except Exception as e:
        print(f"❌ rundll32 실행 실패: {e}")

    # wslview는 Firefox를 열 수 있으므로 사용하지 않음
    print(f"⚠️ wslview는 Firefox를 열 수 있어 건너뜀")

    # 모든 자동 실행 방법 실패
    print(f"\n⚠️  자동 브라우저 실행이 실패했습니다.")
    print(f"💡 이는 정상적인 현상입니다. 위에 표시된 URL을 수동으로 사용하세요.")
    print(f"")
    print(f"🔗 다시 한번 URL: {windows_url}")
    print(f"📋 이 URL을 복사하여 Windows 브라우저에서 열어주세요!")
    print(f"="*60 + "\n")


def get_browser_opener() -> Callable[[str], None]:
    """
    獲取瀏覽器開啟函數，WSL환경에서는 강제로 Windows 브라우저 사용

    Returns:
        Callable: 瀏覽器開啟函數
    """
    # WSL 환경 강제 감지 - 현재 환경이 확실히 WSL이므로 강제로 Windows 브라우저 사용
    wsl_detected = is_wsl()

    # 추가 WSL 감지 로직 - 더 확실하게 하기 위해
    if not wsl_detected:
        # Linux 환경에서 /mnt/c가 있으면 WSL로 간주
        if sys.platform.startswith('linux') and os.path.exists('/mnt/c'):
            print("🔍 강제 WSL 감지 - Linux + /mnt/c 존재")
            wsl_detected = True

    if wsl_detected:
        print("🔍 WSL 환경 감지 - Windows 브라우저 사용")
        return open_url_in_windows_browser
    else:
        print("🔍 일반 환경 - 기본 브라우저 사용")
        return webbrowser.open


# WSL 환경에서 webbrowser.open 호출을 차단하는 보안 조치
def _secure_webbrowser_open(url: str) -> None:
    """WSL 환경에서 webbrowser.open 대신 Windows 브라우저 사용"""
    if is_wsl():
        print(f"🚫 WSL에서 webbrowser.open 차단됨 - Windows Edge 브라우저로 강제 리다이렉트: {url}")
        open_url_in_windows_browser(url)
    else:
        print(f"✅ 일반 환경에서 webbrowser.open 사용: {url}")
        # 원본 webbrowser.open 사용
        import webbrowser as wb
        wb.open(url)

# WSL 환경 강제 감지 및 보안 오버라이드 적용
def _detect_and_setup_wsl():
    """WSL 환경을 감지하고 보안 오버라이드를 설정"""
    wsl_detected = False

    # 여러 방법으로 WSL 감지
    wsl_env_vars = ['WSL_DISTRO_NAME', 'WSLENV', 'WSL_INTEROP']
    for env_var in wsl_env_vars:
        if os.getenv(env_var):
            print(f"🔍 WSL 감지됨 (환경변수 {env_var})")
            wsl_detected = True
            break

    # /proc/version 확인
    if not wsl_detected:
        try:
            if os.path.exists('/proc/version'):
                with open('/proc/version', 'r') as f:
                    version_info = f.read().lower()
                    if 'microsoft' in version_info or 'wsl' in version_info:
                        print(f"🔍 WSL 감지됨 (/proc/version)")
                        wsl_detected = True
        except:
            pass

    # Linux + /mnt/c 확인
    if not wsl_detected:
        if sys.platform.startswith('linux') and os.path.exists('/mnt/c'):
            print(f"🔍 WSL 감지됨 (Linux + /mnt/c)")
            wsl_detected = True

    if wsl_detected:
        print("🛡️ WSL 환경에서 webbrowser.open 보안 오버라이드 적용 - Firefox 실행 차단")
        webbrowser.open = _secure_webbrowser_open

        # 추가 보안: subprocess를 통한 Firefox 실행도 차단
        original_popen = subprocess.Popen

        def _secure_popen(*args, **kwargs):
            """WSL에서 Firefox 실행을 차단하는 보안 래퍼"""
            if args and len(args) > 0:
                cmd = args[0]
                if isinstance(cmd, list) and len(cmd) > 0:
                    cmd_name = str(cmd[0]).lower()
                    # Firefox 관련 명령 차단
                    if any(firefox_cmd in cmd_name for firefox_cmd in ['firefox', 'mozilla']):
                        print(f"🚫 WSL에서 Firefox 실행 차단됨: {cmd}")
                        print(f"   대신 Edge 브라우저로 URL 열기 시도")
                        # URL이 있다면 Edge로 열기
                        for arg in cmd[1:]:
                            if str(arg).startswith('http'):
                                open_url_in_windows_browser(str(arg))
                                break
                        # 더미 프로세스 반환
                        return original_popen(['echo', 'Firefox blocked in WSL'],
                                             stdout=subprocess.DEVNULL,
                                             stderr=subprocess.DEVNULL)

            return original_popen(*args, **kwargs)

        subprocess.Popen = _secure_popen
        print("🛡️ WSL 환경에서 subprocess.Popen Firefox 차단 오버라이드 적용")

    return wsl_detected

# 모듈 로딩 시점에서 WSL 환경 설정
_wsl_environment = _detect_and_setup_wsl()
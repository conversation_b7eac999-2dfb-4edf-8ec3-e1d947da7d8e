#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
網絡工具函數
============

提供網絡相關的工具函數，如端口檢測等。
"""

import socket
import subprocess
import os
from typing import Optional, List


def find_free_port(start_port: int = 8765, max_attempts: int = 100, preferred_port: int = 8765) -> int:
    """
    尋找可用的端口，優先使用偏好端口
    
    Args:
        start_port: 起始端口號
        max_attempts: 最大嘗試次數
        preferred_port: 偏好端口號（用於保持設定持久性）
        
    Returns:
        int: 可用的端口號
        
    Raises:
        RuntimeError: 如果找不到可用端口
    """
    # 首先嘗試偏好端口（通常是 8765）
    if is_port_available("127.0.0.1", preferred_port):
        return preferred_port
    
    # 如果偏好端口不可用，嘗試其他端口
    for i in range(max_attempts):
        port = start_port + i
        if port == preferred_port:  # 跳過已經嘗試過的偏好端口
            continue
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.bind(("127.0.0.1", port))
                return port
        except OSError:
            continue
    
    raise RuntimeError(f"無法在 {start_port}-{start_port + max_attempts - 1} 範圍內找到可用端口")


def is_port_available(host: str, port: int) -> bool:
    """
    檢查端口是否可用
    
    Args:
        host: 主機地址
        port: 端口號
        
    Returns:
        bool: 端口是否可用
    """
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.bind((host, port))
            return True
    except OSError:
        return False 


def is_wsl() -> bool:
    """
    检测是否在WSL环境中运行
    
    Returns:
        bool: True if running in WSL, False otherwise
    """
    try:
        # 检查 /proc/version 文件是否包含 WSL 标识
        if os.path.exists('/proc/version'):
            with open('/proc/version', 'r') as f:
                version_info = f.read().lower()
                return 'microsoft' in version_info or 'wsl' in version_info
    except:
        pass
    
    # 检查环境变量
    if os.getenv('WSL_DISTRO_NAME') or os.getenv('WSLENV'):
        return True
    
    return False


def get_wsl_host_ip() -> Optional[str]:
    """
    获取WSL环境中Windows主机的IP地址
    
    Returns:
        Optional[str]: Windows主机IP地址，如果获取失败返回None
    """
    try:
        # 方法1: 通过 /etc/resolv.conf 获取
        if os.path.exists('/etc/resolv.conf'):
            with open('/etc/resolv.conf', 'r') as f:
                for line in f:
                    if line.startswith('nameserver'):
                        ip = line.split()[1]
                        # WSL2的nameserver通常是Windows主机IP
                        if ip and ip != '127.0.0.1':
                            return ip
    except:
        pass
    
    try:
        # 方法2: 通过默认网关获取
        result = subprocess.run(['ip', 'route', 'show', 'default'], 
                              capture_output=True, text=True, check=True)
        output = result.stdout.strip()
        if 'via' in output:
            # 解析: default via ************ dev eth0
            parts = output.split()
            via_index = parts.index('via')
            if via_index + 1 < len(parts):
                return parts[via_index + 1]
    except:
        pass
    
    return None


def get_local_ip_addresses() -> List[str]:
    """
    获取本机的所有IP地址，WSL环境下包含Windows主机IP
    
    Returns:
        List[str]: IP地址列表
    """
    ip_addresses = []
    
    # 如果是WSL环境，优先添加Windows主机IP
    if is_wsl():
        wsl_host_ip = get_wsl_host_ip()
        if wsl_host_ip:
            ip_addresses.append(wsl_host_ip)
    
    try:
        # 获取主机名
        hostname = socket.gethostname()
        # 通过主机名获取IP地址
        local_ip = socket.gethostbyname(hostname)
        if local_ip and local_ip != '127.0.0.1' and local_ip not in ip_addresses:
            ip_addresses.append(local_ip)
    except:
        pass
    
    try:
        # 尝试连接外部地址来获取本地IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            if local_ip and local_ip not in ip_addresses:
                ip_addresses.append(local_ip)
    except:
        pass
    
    # 如果没有找到其他IP，至少返回localhost
    if not ip_addresses:
        ip_addresses.append("127.0.0.1")
    
    return ip_addresses


def get_primary_ip_address() -> str:
    """
    获取主要的IP地址（用于外部访问），WSL环境下优先返回Windows主机IP
    
    Returns:
        str: 主要IP地址
    """
    ip_addresses = get_local_ip_addresses()
    
    # 优先返回非localhost的IP
    for ip in ip_addresses:
        if ip != "127.0.0.1":
            return ip
    
    # 如果只有localhost，返回它
    return ip_addresses[0] if ip_addresses else "127.0.0.1" 